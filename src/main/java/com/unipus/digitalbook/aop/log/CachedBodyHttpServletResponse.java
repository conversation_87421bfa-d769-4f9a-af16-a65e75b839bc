package com.unipus.digitalbook.aop.log;

import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.WriteListener;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpServletResponseWrapper;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;

/**
 * HttpServletResponse wrapper that caches the response body.
 */
public class CachedBodyHttpServletResponse extends HttpServletResponseWrapper {

    private final ByteArrayOutputStream content = new ByteArrayOutputStream();
    private ServletOutputStream outputStream;
    private PrintWriter writer;
    private String characterEncoding;

    public CachedBodyHttpServletResponse(HttpServletResponse response) {
        super(response);
    }

    @Override
    public ServletOutputStream getOutputStream() throws IOException {
        if (writer != null) {
            throw new IllegalStateException("getWriter() has already been called on this response.");
        }

        if (outputStream == null) {
            outputStream = new TeeServletOutputStream(super.getOutputStream(), content);
        }
        return outputStream;
    }

    @Override
    public PrintWriter getWriter() throws IOException {
        if (outputStream != null) {
            throw new IllegalStateException("getOutputStream() has already been called on this response.");
        }

        if (writer == null) {
            characterEncoding = getCharacterEncoding();
            if (characterEncoding == null) {
                characterEncoding = StandardCharsets.UTF_8.name();
                setCharacterEncoding(characterEncoding);
            }
            writer = new PrintWriter(new OutputStreamWriter(new TeeServletOutputStream(super.getOutputStream(), content), characterEncoding));
        }
        return writer;
    }

    public String getContentAsString() {
        if (writer != null) {
            writer.flush();
        }
        if (characterEncoding != null) {
            return content.toString(StandardCharsets.UTF_8);
        }
        return content.toString();
    }

    public void close() throws IOException {
        if (writer != null) {
            writer.close();
        }
        if (outputStream != null) {
            outputStream.close();
        }
    }

    private static class TeeServletOutputStream extends ServletOutputStream {

        private final ServletOutputStream original;
        private final ByteArrayOutputStream copy;

        public TeeServletOutputStream(ServletOutputStream original, ByteArrayOutputStream copy) {
            this.original = original;
            this.copy = copy;
        }

        @Override
        public void write(int b) throws IOException {
            original.write(b);
            copy.write(b);
        }

        @Override
        public boolean isReady() {
            return original.isReady();
        }

        @Override
        public void setWriteListener(WriteListener writeListener) {
            original.setWriteListener(writeListener);
        }
    }

    /**
     * 获取当前缓存的数据大小
     *
     * @return 缓存数据的字节数
     */
    public int getCachedContentSize() {
        return content.size();
    }

    /**
     * 临时读取指定长度的缓存数据并转换为字符串，不影响缓存数据的最终输出
     * 超过指定长度时进行优化处理，避免完整数据复制
     *
     * @param maxLength 最大读取字节数
     * @return 指定长度的缓存数据字符串
     * @throws IllegalArgumentException 如果长度参数无效
     */
    public String peekContentAsString(int maxLength) {
        if (maxLength <= 0) {
            return "";
        }

        // 刷新writer确保所有数据都写入到缓存中
        if (writer != null) {
            writer.flush();
        }

        int currentSize = content.size();

        // 如果缓存为空
        if (currentSize == 0) {
            return "";
        }

        // 如果请求的长度大于等于缓存大小，返回全部数据
        if (maxLength >= currentSize) {
            return getContentAsString();
        }

        // 优化处理：只读取指定长度的数据
        byte[] bytes = readPartialContent(maxLength);

        return characterEncoding == null ? new String(bytes) : new String(bytes, StandardCharsets.UTF_8);
    }

    /**
     * 优化的部分内容读取方法
     * 使用安全的标准方法，兼容所有Java版本
     */
    private byte[] readPartialContent(int length) {
        byte[] cachedBytes = content.toByteArray();

        // 如果请求长度大于等于实际长度，直接返回全部数据
        if (length >= cachedBytes.length) {
            return cachedBytes;
        }

        // 创建指定长度的结果数组
        byte[] result = new byte[length];
        System.arraycopy(cachedBytes, 0, result, 0, length);
        return result;
    }

}
