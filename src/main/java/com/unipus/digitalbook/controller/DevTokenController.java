package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.common.exception.business.BizException;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.common.utils.JwtUtil;
import com.unipus.digitalbook.conf.cache.CacheManagerConfig;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.constants.CacheConstant;
import com.unipus.digitalbook.model.constants.WebConstant;
import com.unipus.digitalbook.model.entity.CurrentUserInfo;
import com.unipus.digitalbook.model.enums.ResultMessage;
import com.unipus.digitalbook.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 开发测试环境专用Token控制器
 * 仅在非生产环境启用
 */
@Tag(name = "开发测试Token", description = "仅限开发测试环境使用的Token生成接口")
@RestController
@RequestMapping("/auth")
@Slf4j
@Profile({"local", "dev", "test"})
public class DevTokenController {

    @Resource
    private CacheManagerConfig cacheManagerConfig;
    @Resource
    private Environment environment;
    @Resource
    private JwtUtil jwtUtil;
    @Resource
    private UserService userService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    public static final int TOKEN_VALID_HOURS = 24*7;

    /**
     * 生成开发测试环境专用token
     */
    @GetMapping("/accessToken")
    @Operation(summary = "获取开发测试Token", description = "仅限开发测试环境使用，需要提供开发密钥和通过IP白名单验证")
    public Response<HashMap<String, Object>> generateDevToken(
            @RequestParam(value = "userId") Long userId,
            @RequestParam(value = "orgId") Long orgId) {

        // 1. 用户信息验证
        CurrentUserInfo currentUserInfo = validateUser(userId, orgId);
        // 2. 生成安全token
        String token = generateSecureToken(currentUserInfo, userId);
        // 3. 构建响应
        return Response.success("开发Token生成成功", buildTokenResponse(token));
    }

    /**
     * 验证用户信息
     */
    private CurrentUserInfo validateUser(Long userId, Long orgId) {
        CurrentUserInfo currentUserInfo = userService.getCurrentUserInfo(userId, orgId);
        if (currentUserInfo == null) {
            throw new BizException(ResultMessage.SYS_ONLY_DEV_ENV_USE_ERROR, String.format("用户不存在: userId=%d, orgId=%d", userId, orgId));
        }
        return currentUserInfo;
    }

    /**
     * 生成安全的token
     */
    private String generateSecureToken(CurrentUserInfo currentUserInfo, Long userId) {
        // 生成短期serviceTicket
        String serviceTicket = UUID.randomUUID().toString();
        String ticketKey = CacheConstant.REDIS_AUTH_PREFIX + userId;
        String userInfoKey = CacheConstant.REDIS_USR_PREFIX + userId;

        // 缓存serviceTicket
        stringRedisTemplate.opsForValue().set(ticketKey, serviceTicket, TOKEN_VALID_HOURS, TimeUnit.HOURS );

        // 缓存用户信息
        String userInfo = JsonUtil.toJsonString(currentUserInfo);
        stringRedisTemplate.opsForValue().set(userInfoKey, userInfo, TOKEN_VALID_HOURS, TimeUnit.HOURS);

        // 构建JWT claims
        Map<String, Object> claims = new HashMap<>();
        claims.put(WebConstant.JWT_SERVICE_TICKET, serviceTicket);
        claims.put(WebConstant.JWT_USER_ID, userId);
        claims.put("iat", Instant.now().getEpochSecond());
        claims.put("exp", Instant.now().plusSeconds(TOKEN_VALID_HOURS * 3600).getEpochSecond());
        claims.put("env", environment.getActiveProfiles());

        return jwtUtil.generateToken(claims, currentUserInfo.getUserInfo().getCellPhone());
    }

    private HashMap<String, Object> buildTokenResponse(String token) {
        HashMap<String, Object> response = new HashMap<>();
        response.put("access_token", token);
        response.put("environment", String.join(",", environment.getActiveProfiles()));
        return response;
    }

    @GetMapping("/getCacheStats")
    @Operation(summary = "获取本地缓存状态", description = "获取本地缓存状态")
    public String generateDevToken() {
        return cacheManagerConfig.getCacheStats();
    }
}