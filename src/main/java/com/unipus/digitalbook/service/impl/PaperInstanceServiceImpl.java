package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.common.exception.business.BizException;
import com.unipus.digitalbook.dao.PaperExtendPOMapper;
import com.unipus.digitalbook.dao.PaperScoreBatchPOMapper;
import com.unipus.digitalbook.model.entity.UserAccessInfo;
import com.unipus.digitalbook.model.entity.paper.Paper;
import com.unipus.digitalbook.model.entity.paper.PaperInstance;
import com.unipus.digitalbook.model.entity.paper.PaperInstanceCreationContext;
import com.unipus.digitalbook.model.enums.PaperPreviewModeEnum;
import com.unipus.digitalbook.model.enums.PaperSubmitStatusEnum;
import com.unipus.digitalbook.model.enums.PaperTypeEnum;
import com.unipus.digitalbook.model.po.paper.PaperPO;
import com.unipus.digitalbook.model.po.paper.PaperScoreBatchPO;
import com.unipus.digitalbook.service.PaperInstanceService;
import com.unipus.digitalbook.service.factory.paper.instance.PaperInstanceStrategy;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 试卷实例服务实现
 */
@Service
@Slf4j
public class PaperInstanceServiceImpl implements PaperInstanceService {

    @Resource
    private PaperExtendPOMapper paperExtendPOMapper;
    @Resource
    private PaperScoreBatchPOMapper paperScoreBatchPOMapper;
    @Resource
    private List<PaperInstanceStrategy> paperInstanceStrategies;

    // 根据试卷类型获取对应的试卷实例策略对象
    private PaperInstanceStrategy getPaperInstanceFactory(PaperTypeEnum paperType){
        if(CollectionUtils.isEmpty(paperInstanceStrategies)){
            throw new IllegalStateException("未配置试卷实例策略");
        }

        PaperInstanceStrategy paperInstanceProcessor = paperInstanceStrategies.stream()
                .filter(strategy -> strategy.getPaperType().match(paperType))
                .findFirst()
                .orElse(null);
        if(paperInstanceProcessor ==null){
            log.debug("试卷类型不支持:{}", paperType);
            throw new BizException("试卷类型不支持:" + paperType);
        }
        return paperInstanceProcessor;
    }

    /**
     * 生成预览模式试卷
     * @param param 试卷实例参数
     *              paperId 试卷ID
     *              openId 用户ssoID
     *              tenantId 租户ID
     *              testMode 试卷模式（非诊断卷可为空值）
     * @return 试卷实例
     */
    @Override
    public PaperInstance generatePreviewPaperInstance(PaperInstance param){
        // 1. 获取试卷信息
        Paper paper = getPaperWithVersion(param.getPaperId(), param.getVersionNumber());

        // 2. 使用对应的策略创建试卷实例
        PaperInstanceStrategy strategy = getPaperInstanceFactory(paper.getPaperType());

        // 3. 初始化预览卷状态(删除历史作答)
        strategy.initPreviewPaperStatus(paper, param.getOpenId(), param.getTenantId(), param.getClearPreviewHistory());

        // 4. 创建预览模式试卷实例
        PaperInstanceCreationContext context = new PaperInstanceCreationContext(paper, param.getOpenId(), param.getTenantId(), param.getTestMode());
        if( PaperPreviewModeEnum.TEACHER.match(param.getPreviewMode())) {
            return strategy.createPreviewPaperInstance(context);
        }else{
            return strategy.createRealPaperInstance(context);
        }
    }

    /**
     * 生成实际作答试卷
     * @param param 试卷实例参数
     *              paperId 试卷ID
     *              versionNumber 版本号
     *              openId 用户ssoID
     *              tenantId 租户ID
     *              testMode 试卷模式（非诊断卷可为空值）
     *              instanceId 试卷实例ID/轮次ID (可以为空值)
     * @return 试卷实例
     */
    @Override
    public PaperInstance generateRealPaperInstance(PaperInstance param) {
        // 1. 获取试卷信息
        Paper paper = getPaperWithVersion(param.getPaperId(), param.getVersionNumber());

        // 2. 使用对应的策略创建试卷实例
        PaperInstanceStrategy strategy = getPaperInstanceFactory(paper.getPaperType());

        // 3. 获取缓存中的实例
        PaperInstance paperInstance = strategy.loadPaperInstance(param.getInstanceId());
        if (paperInstance != null) {
            // 返回缓存中的试卷实例
            return paperInstance;
        }

        // 4. 创建新的试卷实例
        PaperInstanceCreationContext context = new PaperInstanceCreationContext(paper, param.getOpenId(),
                param.getTenantId(), param.getTestMode());
        return strategy.createRealPaperInstance(context);
    }

    /**
     * 获取用户特定试卷的最近一次试卷实例信息
     *
     * @param param  试卷实例参数
     *               paperId 试卷ID
     *               versionNumber 版本号
     *               openId 用户ssoID
     *               tenantId 租户ID
     * @param submitStatus 试卷提交状态（0：未提交/1：已提交）
     * @return 用户作答记录
     */
    @Override
    public PaperInstance getLatestPaperInstance(PaperInstance param, Integer submitStatus) {
        // 1. 获取试卷信息
        Paper paper = getPaperWithVersion(param.getPaperId(), param.getVersionNumber());

        // 2. 使用对应的策略创建试卷实例
        PaperInstanceStrategy strategy = getPaperInstanceFactory(paper.getPaperType());

        // 3. 检查缓存
        return strategy.getLatestPaperInstance(paper, param.getOpenId(), param.getTenantId(), param.getTestMode(), submitStatus);
    }

    /**
     * 获取指定版本试卷详情
     * @param paperId 试卷ID
     * @param versionNumber 试卷版本号
     * @return 试卷信息
     */
    private Paper getPaperWithVersion(String paperId, String versionNumber) {
        PaperPO paperPO = paperExtendPOMapper.selectPaperDetail(paperId, versionNumber, true);
        if(paperPO==null){
            log.error("未能查询到试卷基本信息，paperId: {}, versionNumber: {}", paperId, versionNumber);
            throw new BizException("未能查询到试卷基本信息");
        }
        return paperPO.toEntity();
    }

    /***
     * 加载试卷实例
     * 取得已经生成的试卷实例
     * @param instanceId 试卷实例ID/轮次ID
     * @param paperType 试卷类型
     * @return 试卷实例
     */
    @Override
    public PaperInstance loadPaperInstance(String instanceId, PaperTypeEnum paperType){
        return getPaperInstanceFactory(paperType).loadPaperInstance(instanceId);
    }

    /**
     * 获取用户已作答试卷的解析
     * @param instanceId 试卷实例ID/轮次ID
     * @return 试卷解析
     */
    @Override
    public PaperInstance getPaperAnalysis(String instanceId) {
        // 1. 检查试卷是否已经作答完成，并提交了作答记录(返回试卷实例类型)
        PaperTypeEnum paperType = getPaperTypeByInstanceId(instanceId);

        // 2. 使用对应的策略创建试卷实例（优化版本，传入已查询的批次信息）
        PaperInstanceStrategy strategy = getPaperInstanceFactory(paperType);

        // 3. 获取试卷实例（使用优化版本，避免重复查询）
        PaperInstance paperInstance = strategy.loadPaperInstance(instanceId);
        if(paperInstance==null){
            log.error("试卷实例不存在，instanceId:{}", instanceId);
            throw new BizException("试卷实例不存在");
        }
        return paperInstance;
    }

    // 取得缓存的试卷实例类型
    private PaperTypeEnum getPaperTypeByInstanceId(String instanceId) {

        // 检查试卷是否已经作答完成，并提交了作答记录
        PaperScoreBatchPO paperScoreBatchPO = paperScoreBatchPOMapper.gePaperScoreBatchByInstanceId(instanceId, null);
        if(paperScoreBatchPO==null){
            log.debug("试卷未提交，无法查看试卷分析。instanceId:{}", instanceId);
            throw new BizException("试卷未提交，无法查看试卷分析");
        }

        PaperTypeEnum paperType = PaperTypeEnum.getByCode(paperScoreBatchPO.getPaperType());
        PaperSubmitStatusEnum submitStatus = PaperSubmitStatusEnum.getEnumByCode(paperScoreBatchPO.getStatus());
        if(!PaperTypeEnum.CHALLENGE.match(paperType) && PaperSubmitStatusEnum.UNSUBMITTED.match(submitStatus)){
            log.debug("试卷未提交，无法查看试卷分析。instanceId:{}", instanceId);
            throw new BizException("试卷未提交，无法查看试卷分析");
        }
        return paperType;
    }

    /**
     * 获取用户特定试卷的最近一次提交的试卷版本
     * @param paperId 试卷ID
     * @param userAccessInfo 用户访问信息
     * @return 最近一次提交的试卷版本
     */
    @Override
    public String getLatestSubmittedPaperVersion(String paperId, UserAccessInfo userAccessInfo) {
        String openId = userAccessInfo.getOpenId();
        Long tenantId = userAccessInfo.getTenantId();
        // 查询该用户试卷所有版本中最近一次的提交成绩记录
        PaperScoreBatchPO condition = new PaperScoreBatchPO(paperId, null, openId, tenantId,
                PaperSubmitStatusEnum.SUBMITTED.getCode());
        PaperScoreBatchPO paperScoreBatchPO = paperScoreBatchPOMapper.getLatestPaperScoreBatch(condition);
        if(paperScoreBatchPO==null){
            log.debug("未找到未提交成绩记录:{}，{}，{}", openId, tenantId, paperId);
            return null;
        }else {
            String latestPaperVersionNumber = paperScoreBatchPO.getPaperVersionNumber();
            log.debug("找到未提交成绩记录:{}，{}，{}，{}", openId, tenantId, paperId, latestPaperVersionNumber);
            return latestPaperVersionNumber;
        }
    }

}
